/* @settings

name: Media Extended Settings
id: mx-media-view
settings:
    -
        id: mx-max-embed-height-title
        title: Media Embed Settings
        type: heading
        level: 2
    -
        id: mx-max-embed-height
        title: Maximum Embed Height
        description: Maximum height for media embeds
        type: variable-text
        default: 60vh

*/

@property --tw-translate-x{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-y{syntax:"*";inherits:false;initial-value:0}@property --tw-translate-z{syntax:"*";inherits:false;initial-value:0}@property --tw-rotate-x{syntax:"*";inherits:false;initial-value:rotateX(0)}@property --tw-rotate-y{syntax:"*";inherits:false;initial-value:rotateY(0)}@property --tw-rotate-z{syntax:"*";inherits:false;initial-value:rotateZ(0)}@property --tw-skew-x{syntax:"*";inherits:false;initial-value:skewX(0)}@property --tw-skew-y{syntax:"*";inherits:false;initial-value:skewY(0)}@property --tw-duration{syntax:"*";inherits:false}@property --tw-border-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-font-weight{syntax:"*";inherits:false}@property --tw-space-y-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-space-x-reverse{syntax:"*";inherits:false;initial-value:0}@property --tw-gradient-position{syntax:"*";inherits:false}@property --tw-gradient-from{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-via{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-to{syntax:"<color>";inherits:false;initial-value:#0000}@property --tw-gradient-stops{syntax:"*";inherits:false}@property --tw-gradient-via-stops{syntax:"*";inherits:false}@property --tw-gradient-from-position{syntax:"<length-percentage>";inherits:false;initial-value:0%}@property --tw-gradient-via-position{syntax:"<length-percentage>";inherits:false;initial-value:50%}@property --tw-gradient-to-position{syntax:"<length-percentage>";inherits:false;initial-value:100%}@property --tw-leading{syntax:"*";inherits:false}@property --tw-tracking{syntax:"*";inherits:false}@property --tw-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-shadow-color{syntax:"*";inherits:false}@property --tw-inset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-shadow-color{syntax:"*";inherits:false}@property --tw-ring-color{syntax:"*";inherits:false}@property --tw-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-inset-ring-color{syntax:"*";inherits:false}@property --tw-inset-ring-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-ring-inset{syntax:"*";inherits:false}@property --tw-ring-offset-width{syntax:"<length>";inherits:false;initial-value:0}@property --tw-ring-offset-color{syntax:"*";inherits:false;initial-value:#fff}@property --tw-ring-offset-shadow{syntax:"*";inherits:false;initial-value:0 0 #0000}@property --tw-outline-style{syntax:"*";inherits:false;initial-value:solid}@property --tw-blur{syntax:"*";inherits:false}@property --tw-brightness{syntax:"*";inherits:false}@property --tw-contrast{syntax:"*";inherits:false}@property --tw-grayscale{syntax:"*";inherits:false}@property --tw-hue-rotate{syntax:"*";inherits:false}@property --tw-invert{syntax:"*";inherits:false}@property --tw-opacity{syntax:"*";inherits:false}@property --tw-saturate{syntax:"*";inherits:false}@property --tw-sepia{syntax:"*";inherits:false}@property --tw-drop-shadow{syntax:"*";inherits:false}@property --tw-backdrop-blur{syntax:"*";inherits:false}@property --tw-backdrop-brightness{syntax:"*";inherits:false}@property --tw-backdrop-contrast{syntax:"*";inherits:false}@property --tw-backdrop-grayscale{syntax:"*";inherits:false}@property --tw-backdrop-hue-rotate{syntax:"*";inherits:false}@property --tw-backdrop-invert{syntax:"*";inherits:false}@property --tw-backdrop-opacity{syntax:"*";inherits:false}@property --tw-backdrop-saturate{syntax:"*";inherits:false}@property --tw-backdrop-sepia{syntax:"*";inherits:false}@property --tw-ease{syntax:"*";inherits:false}@property --tw-scale-x{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-y{syntax:"*";inherits:false;initial-value:1}@property --tw-scale-z{syntax:"*";inherits:false;initial-value:1}.mx-connect-action:not([data-active]){display:none}.mx-connect-action[data-connected]{color:var(--text-accent)}.mx-track-view.view-content{padding:0}.mx-track-view.view-content .shadow-root{height:100%}.mx-transcript-select{min-width:500px;max-width:800px}.mx-transcript-group{margin-bottom:var(--size-4-6)}.mx-transcript-group:last-child{margin-bottom:0}.mx-transcript-group-title{margin:0 0 var(--size-4-2) 0;padding:var(--size-2-1) var(--size-4-2);font-size:var(--font-ui-small);font-weight:var(--font-semibold);color:var(--text-muted);text-transform:uppercase;letter-spacing:.05em;border-bottom:1px solid var(--background-modifier-border)}.mx-transcript-list{display:flex;flex-direction:column;gap:var(--size-2-1)}.mx-transcript-item{display:flex;align-items:center;gap:var(--size-4-3);padding:var(--size-4-3);background-color:var(--background-secondary);border:1px solid var(--background-modifier-border);border-radius:var(--radius-s);transition:background-color .1s ease-in-out}.mx-transcript-info{flex:1;min-width:0;display:flex;flex-direction:column;gap:var(--size-2-1)}.mx-transcript-title{font-weight:var(--font-medium);color:var(--text-normal);line-height:var(--line-height-tight);word-break:break-word}.mx-transcript-meta{display:flex;flex-wrap:wrap;gap:var(--size-2-2);font-size:var(--font-ui-smaller);color:var(--text-muted)}.mx-transcript-meta>span{padding:var(--size-2-1) var(--size-2-3);background-color:var(--background-modifier-form-field);border-radius:var(--radius-s);white-space:nowrap}.mx-transcript-meta .mx-transcript-language{background-color:var(--interactive-accent);color:var(--text-on-accent)}.mx-transcript-kind{text-transform:capitalize}.mx-transcript-format{font-family:var(--font-monospace);font-size:var(--font-ui-smaller)}.mx-transcript-default{background-color:var(--interactive-success);color:var(--text-on-accent)}.mx-transcript-label{font-style:italic;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mx-transcript-path{font-size:var(--font-ui-smaller);color:var(--text-faint);font-family:var(--font-monospace);overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mx-transcript-select-btn{flex-shrink:0}.login-modal-content{padding:1.5rem;max-width:400px;margin:0 auto}.login-header{text-align:center;margin-bottom:2rem}.login-header h3{margin:0 0 .5rem;font-size:1.5rem;font-weight:600;color:var(--text-normal)}.login-subtitle{margin:0;color:var(--text-muted);font-size:.875rem;line-height:1.4}.login-features{display:flex;flex-direction:column;gap:1.25rem;margin-bottom:2rem}.login-feature-item{display:flex;align-items:flex-start;gap:1rem}.login-feature-icon{flex-shrink:0;margin-top:2px;color:var(--interactive-accent)}.login-feature-icon svg{width:20px;height:20px}.login-feature-text{text-align:left}.login-feature-text strong{display:block;font-weight:600;color:var(--text-normal);font-size:.875rem;margin-bottom:.25rem}.login-feature-text p{margin:0;font-size:.875rem;color:var(--text-muted);line-height:1.4}.login-options{display:flex;flex-direction:column;gap:.75rem;margin-bottom:1.5rem}.login-button-container{width:100%}.login-button{width:100%;justify-content:center;min-height:44px;display:flex;align-items:center;gap:.75rem;position:relative;cursor:pointer}.login-button .svg-icon{flex-shrink:0;width:20px;height:20px}.login-button.google-login{background:var(--background-primary);color:var(--text-normal)}.login-button.google-login:hover{background:var(--background-modifier-hover)}.login-button.github-login{background:var(--text-normal);color:var(--background-primary)}.login-button.github-login:hover{background:var(--text-muted)}.login-button.github-login .svg-icon{color:inherit}.login-divider{display:flex;align-items:center;margin:1.5rem 0;gap:1rem}.login-divider-line{flex:1;height:1px;background:var(--background-modifier-border)}.login-divider-text{font-size:.75rem;color:var(--text-muted);text-transform:uppercase;letter-spacing:.5px}.login-email-section{margin-bottom:1rem}.login-email-form{display:flex;flex-direction:column;gap:1rem}.login-label{display:block;margin-bottom:.5rem;font-size:.875rem;font-weight:500;color:var(--text-normal)}.login-input{width:100%;padding:.875rem 1rem;border:1px solid var(--background-modifier-border);border-radius:.5rem;background:var(--background-primary);font-size:.875rem;color:var(--text-normal);transition:border-color .2s ease;box-sizing:border-box}.login-input:focus{outline:none;border-color:var(--interactive-accent);box-shadow:0 0 0 2px var(--interactive-accent-hover)}.login-input::placeholder{color:var(--text-muted)}.login-footer{margin-top:2rem;text-align:center}.login-terms{margin:0;font-size:.75rem;color:var(--text-muted);line-height:1.4;text-wrap:balance}@media (max-width: 480px){.login-modal-content{padding:1rem}.login-header h3{font-size:1.25rem}}.login-input:focus-visible{outline:none;border-color:var(--interactive-accent);box-shadow:0 0 0 2px var(--interactive-accent-hover)}.mx-login-notice{display:flex;flex-direction:column;gap:var(--size-4-2);padding:.75em 0;max-width:300px}.mx-login-notice-header{display:flex;align-items:center;justify-content:space-between;gap:var(--size-4-2)}.mx-login-notice-action{display:flex;justify-content:flex-end;margin-top:var(--size-4-1)}.mx-dialog.image-clip{width:max(var(--dialog-width),700px);max-width:min(90vw,800px)}.mx-dialog.track-import{overflow-x:hidden;width:max(var(--dialog-width),700px);max-width:min(90vw,800px)}.mx-dialog.youtube-subtitle-import{width:max(var(--dialog-width),700px);max-width:min(90vw,800px)}.mx-dialog.mx-pwd-manager{overflow-x:hidden;width:max(var(--dialog-width),700px);max-width:min(90vw,800px);height:80vh;display:flex;flex-direction:column}.mx-dialog.mx-pwd-manager .modal-content{display:flex;flex-direction:column;flex:1}.mx-dialog.mx-pwd-manager .pwd-manager-description{padding:var(--size-4-2) var(--size-4-4);color:var(--text-muted);font-size:var(--font-ui-smaller);line-height:var(--line-height-normal);border-bottom:1px solid var(--background-modifier-border);flex-shrink:0}.mx-dialog.mx-pwd-manager .pwd-manager-actions{display:flex;gap:var(--size-4-2);padding:var(--size-4-2) var(--size-4-4);flex-shrink:0}.mx-dialog.mx-pwd-manager .pwd-manager-list{padding:var(--size-4-4);overflow-y:auto;flex:1}.mx-dialog.mx-pwd-manager .pwd-manager-list>*{height:100%}.auth-section{display:contents}.cm-editor ::part(edit-button){display:flex}::part(edit-button){display:none}.mx-media-view.view-content .mx-player-shadow-root{flex:1}.mx-media-view.mx-video-view.view-content .mx-player-shadow-root{height:100cqh;max-width:min(100cqw,calc(100cqh * var(--mx-ratio-w, 16) / var(--mx-ratio-h, 9)))}.mx-media-view.view-content{display:flex;align-items:center;justify-content:center;container-type:size}.mx-media-view.mx-audio-view.view-content{align-items:flex-start}.mx-media-embed.mx-video-view{margin:0 auto;display:flex;align-items:center;justify-content:center;container-type:size;width:min(var(--assigned-width, 100%),100%);aspect-ratio:var(--mx-ratio-w, 16) / var(--mx-ratio-h, 9);max-height:min(var(--assigned-height, 1000vh),var(--mx-max-embed-height, 60vh))}.mx-media-embed.mx-video-view>.mx-player-shadow-root{flex:1;height:100%;max-width:min(100cqw,calc(100cqh * var(--mx-ratio-w, 16) / var(--mx-ratio-h, 9)))}.markdown-source-view.mod-cm6 .cm-content .mx-external-media-embed~img[data-mx-error]{display:none}.markdown-source-view.mod-cm6 .cm-content iframe.external-embed[src*="youtube.com/embed/"]{display:none}.mx-invalid-notice{display:flex!important;min-height:60px;width:100%;border-radius:var(--radius-s);border:1px solid var(--background-modifier-error);padding:.5rem .75rem;align-items:center;justify-content:center;color:var(--text-warning)}
