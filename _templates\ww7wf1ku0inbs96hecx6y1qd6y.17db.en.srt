1
00:00:00,000 --> 00:00:02,000
[MUSIC PLAYING]

2
00:00:03,000 --> 00:00:10,000
ALICE LISAK: Hi.

3
00:00:11,000 --> 00:00:13,000
I'm Alice. LUCAS GONZALEZ: I'm <PERSON>.

4
00:00:14,000 --> 00:00:19,000
We're product managers at Google DeepMind. ALICE LISAK: And today, we are incredibly excited to introduce

5
00:00:20,000 --> -1:-1:-1,000
EmbeddingGemma, our state-of-the-art embedding model designed for mobile-first AI.

6
00:00:00,000 --> 00:00:28,000
LUCAS GONZALEZ: EmbeddingGemma is a 300-million-parameter text

7
00:00:29,000 --> 00:00:35,000
embedding model designed to power generative AI experiences directly on your hardware.

8
00:00:36,000 --> 00:00:44,000
Embeddings are numerical representations of data. This model transforms text like messages, emails, or notes

9
00:00:45,000 --> 00:00:48,000
into a vector of numbers to represent meaning in a high-dimensional space

10
00:00:49,000 --> 00:00:55,000
that a generative model can then use for downstream tasks. ALICE LISAK: EmbeddingGemma is small, fast, and efficient.

11
00:00:56,000 --> 00:01:02,000
Thanks to quantization-aware training, you can run the model with as little as 300 megabytes of RAM

12
00:01:03,000 --> 00:01:09,000
while preserving state-of-the-art quality. It generates embeddings of 768 dimensions,

13
00:01:10,000 --> 00:01:14,000
but thanks to Matryoshka Representation Learning, you can customize the model's output dimensions

14
00:01:15,000 --> 00:01:17,000
and go down to 128.

15
00:01:18,000 --> 00:01:23,000
LUCAS GONZALEZ: Based on the same technology and research that powers our Gemini embedding models,

16
00:01:24,000 --> 00:01:29,000
EmbeddingGemma brings that state-of-the-art capability in a smaller and more lightweight model.

17
00:01:30,000 --> 00:01:36,000
Think high-quality semantic search, fast and relevant information retrieval, or customized classification

18
00:01:37,000 --> 00:01:42,000
and clustering, just to name a few opportunities. ALICE LISAK: EmbeddingGemma achieves the best score

19
00:01:43,000 --> 00:01:50,000
on the comprehensive massive text embedding benchmark for models under 500 million parameters, the gold standard

20
00:01:51,000 --> 00:01:55,000
for text embedding evaluation. Trained across 100-plus languages,

21
00:01:56,000 --> 00:02:02,000
EmbeddingGemma brings proven performance to instantly connect with diverse and global audiences.

22
00:02:03,000 --> 00:02:06,000
LUCAS GONZALEZ: We've engineered EmbeddingGemma specifically for on-device performance

23
00:02:07,000 --> 00:02:13,000
to ensure efficient computations and minimal memory footprint, even on resource-constrained hardware.

24
00:02:14,000 --> 00:02:19,000
EmbeddingGemma facilitates on-device embedding of local documents, so sensitive user data never

25
00:02:20,000 --> 00:02:23,000
leaves the device. And because it works offline, it means

26
00:02:24,000 --> -1:-1:-1,000
frontier search and retrieval features work regardless of connectivity.

27
00:00:00,000 --> 00:02:31,000
ALICE LISAK: Together with our generative models like Gemma 3n,

28
00:02:32,000 --> 00:02:39,000
you can build powerful, mobile-first generative AI experiences and efficient, retrieval-augmented generation

29
00:02:40,000 --> 00:02:43,000
pipelines. This means your applications can now leverage user context

30
00:02:44,000 --> 00:02:49,000
from data to provide more personalized and helpful responses, such as understanding that you

31
00:02:50,000 --> 00:02:54,000
need your carpenter's number for help with damaged floorboards.

32
00:02:55,000 --> 00:02:59,000
Here's an example of what EmbeddingGemma can power. What you are seeing is how a user

33
00:03:00,000 --> 00:03:04,000
can utilize EmbeddingGemma to query previously opened articles or other web pages.

34
00:03:05,000 --> 00:03:12,000
The model embeds each page as it's opened in real time. Then, with a browser extension that uses EmbeddingGemma,

35
00:03:13,000 --> 00:03:16,000
the user can ask a question to retrieve the contextually relevant articles.

36
00:03:17,000 --> -1:-1:-1,000
And because the embeddings are created on-device, all this is happening without leaving the user's hardware.

37
00:00:00,000 --> 00:03:23,000
LUCAS GONZALEZ: And it's designed

38
00:03:24,000 --> 00:03:28,000
with customization in mind. Fine-tune EmbeddingGemma for your domain

39
00:03:29,000 --> 00:03:33,000
or in a particular language. It works across popular tools and platforms such as Hugging

40
00:03:34,000 --> 00:03:38,000
Face and Kaggle. Check out our notebook examples, part of the Gemma Cookbook,

41
00:03:39,000 --> 00:03:40,000
to get started.

42
00:03:41,000 --> 00:03:46,000
ALICE LISAK: Our next generation of on-device embedding models is here, and it's open for everyone.

43
00:03:47,000 --> 00:03:52,000
It's small, fast, and efficient. Download EmbeddingGemma and get started building right now.

44
00:03:53,000 --> 00:03:58,000
LUCAS GONZALEZ: You can find links in the description below. We can't wait to see what EmbeddingGemma unlocks for you.

45
00:03:59,000 --> 00:04:09,000
[MUSIC PLAYING]

