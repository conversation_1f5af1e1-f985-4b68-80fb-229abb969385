// C:\ObsidianVault\Notes-Core\_templates\_scripts\converter.js

const { exec } = require('child_process');
const util = require('util');
// const execPromise = util.promisify(exec);
const path = require('path'); // Use the path module
const execPromise = util.promisify(exec);

async function processYouTubeUrl(tp, youtubeUrl) {
    if (!youtubeUrl) {
        console.error("No YouTube URL provided.");
        new Notice("Error: No YouTube URL provided.");
        return;
    }

    // --- CONFIGURATION ---
    // On Windows, the command is almost always python, not python3
    // In JavaScript strings, you must use double backslashes (\\) to represent a single backslash.
    const pythonPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python.exe';
    // const scriptPath = tp.file.vault.adapter.getFullPath('_templates/_scripts/get_transcript.py');
    // --- <PERSON>ND CONFIGURATION ---

    // Get the vault's absolute path in a reliable way
    const vaultPath = tp.app.vault.adapter.basePath;
    // Construct the absolute path to the python script
    const scriptPath = path.join(vaultPath, '_templates/_scripts', 'get_transcript.py');

    new Notice('Fetching transcript...', 3000);

    try {
        const command = `"${pythonPath}" "${scriptPath}" "${youtubeUrl}"`;
        const { stdout, stderr } = await execPromise(command);

        if (stderr) {
            console.error("Python script error:", stderr);
            new Notice(`Error fetching transcript: ${stderr}`, 10000);
            return;
        }

        // Base64 encode the SRT output to create a data: URL
        const srt_data_url = 'data:text/srt;base64,' + Buffer.from(stdout).toString('base64');
        
        // Generate the Media Extended code block
        const mediaViewBlock = `\`\`\`media-view
- url: ${youtubeUrl}
- track:
    - src: ${srt_data_url}
      label: "English"
      default: true
\`\`\``;
        
        new Notice('Transcript added successfully!', 3000);
        return mediaViewBlock;

    } catch (error) {
        console.error("Execution error:", error);
        new Notice(`Failed to execute script: ${error.message}`, 10000);
        return;
    }
}

module.exports = processYouTubeUrl;