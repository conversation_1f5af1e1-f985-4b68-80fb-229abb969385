<%*
// Test template for V3 converter with better error handling
try {
    new Notice("Loading V3 Converter...", 2000);
    const converter = require(tp.app.vault.adapter.basePath + '/_templates/_scripts/converter_v3.js');
    
    new Notice("V3 Converter loaded successfully!", 2000);
    const result = await converter(tp);
    
    if (result) {
        tR += result;
    } else {
        tR += "No result returned from converter.";
    }
} catch (error) {
    new Notice(`Error loading or running V3 converter: ${error.message}`, 8000);
    console.error('V3 Converter Error:', error);
    
    tR += `# V3 Converter Error

**Error Message:** ${error.message}

**Stack Trace:**
\`\`\`
${error.stack || 'No stack trace available'}
\`\`\`

**Troubleshooting:**
1. Check if the converter_v3.js file exists
2. Verify all required Node.js modules are available
3. Check the console for additional error details
4. Try running YTranscript manually first

**File Path:** ${tp.app.vault.adapter.basePath}/_templates/_scripts/converter_v3.js
`;
}
%>
