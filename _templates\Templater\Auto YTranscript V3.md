<%*
// Automated YTranscript V3 - Focus on programmatic YTranscript execution
try {
    new Notice("🚀 Starting Automated YTranscript V3...", 3000);
    
    // Load the V3 converter
    const converter = require(tp.app.vault.adapter.basePath + '/_templates/_scripts/converter_v3.js');
    
    // Run the converter (it will prompt for URL and handle YTranscript automatically)
    const result = await converter(tp);
    
    if (result) {
        tR += result;
        new Notice("✅ Automated YTranscript V3 completed successfully!", 4000);
    } else {
        tR += "❌ No result returned from V3 converter.";
        new Notice("❌ V3 converter returned no result", 5000);
    }
    
} catch (error) {
    new Notice(`❌ Auto YTranscript V3 Error: ${error.message}`, 8000);
    console.error('Auto YTranscript V3 Error:', error);
    
    tR += `# Auto YTranscript V3 Error

**Error:** ${error.message}

**Stack Trace:**
\`\`\`
${error.stack || 'No stack trace available'}
\`\`\`

## Troubleshooting Steps:

1. **Check YTranscript Plugin:**
   - Ensure YTranscript plugin is installed and enabled
   - Try running YTranscript commands manually first

2. **Manual Workflow:**
   - Use "Process Existing YTranscript" template instead
   - Run YTranscript manually, then process the result

3. **Debug Information:**
   - Run "Debug YTranscript Commands" template to see available commands
   - Check console (Ctrl+Shift+I) for additional error details

**File Path:** ${tp.app.vault.adapter.basePath}/_templates/_scripts/converter_v3.js
`;
}
%>
