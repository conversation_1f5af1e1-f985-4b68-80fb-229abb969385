// C:\ObsidianVault\Notes-Core\_templates\_scripts\converter.js

const { exec } = require('child_process');
const util = require('util');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');
const execPromise = util.promisify(exec);

function generateMxId(length = 26) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

async function processYouTubeUrl(tp, youtubeUrl) {
    new Notice("Creating Synced Transcript...", 3000);

    if (!youtubeUrl || !youtubeUrl.includes('http')) {
        new Notice("Error: Invalid YouTube URL provided.");
        return;
    }
    
    const pythonPath = 'C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\python.exe';
    const vaultPath = tp.app.vault.adapter.basePath;
    const getTranscriptScriptPath = path.join(vaultPath, '_templates/_scripts', 'get_transcript.py');
    
    const subtitlesDir = path.join(vaultPath, 'z_subtitles');
    const mediaLibDir = path.join(vaultPath, 'media-lib');

    try {
        await fs.mkdir(subtitlesDir, { recursive: true });
        await fs.mkdir(mediaLibDir, { recursive: true });
    } catch (err) { /* Handled by file write */ }

    new Notice('Fetching and processing transcript...', 5000);

    try {
        const command = `"${pythonPath}" "${getTranscriptScriptPath}" "${youtubeUrl}"`;
        const { stdout: srtContent, stderr } = await execPromise(command, { maxBuffer: 1024 * 1024 * 10 });

        if (stderr) {
            new Notice(`Python Error: ${stderr}`, 10000);
            return;
        }

        const mediaNoteUID = generateMxId();
        const shortId = crypto.randomBytes(3).toString('hex').substring(0, 4);
        const mediaNoteFileName = `url-autogen-${generateMxId(8)}.md`;
        const srtFileName = `${mediaNoteUID}.${shortId}.en.srt`;
        const srtFilePath = path.join(subtitlesDir, srtFileName);
        
        await fs.writeFile(srtFilePath, srtContent, 'utf8');

        const mediaNoteContent = `---
mx-uid: ${mediaNoteUID}
media: ${youtubeUrl.trim()}
subtitles:
  - "[[${srtFileName}#lang=en&label=English (Synced)]]"
---
This is an auto-generated media note for the Media Extended plugin.`;

        const mediaNotePath = path.join(mediaLibDir, mediaNoteFileName);
        await fs.writeFile(mediaNotePath, mediaNoteContent, 'utf8');
        new Notice(`Media link established successfully!`, 3000);

        // --- FINAL, CLEAN OUTPUT FOR YOUR NOTE ---
        return `
---
Video: ${youtubeUrl.trim()}
Transcript: [[${srtFileName}]]
---

`;

    } catch (error) {
        console.error("Script execution error:", error);
        new Notice(`Failed to execute script: ${error.message}`, 10000);
        return `### Script Error\n\`\`\`\n${error.message}\n\`\`\``;
    }
}

module.exports = processYouTubeUrl;