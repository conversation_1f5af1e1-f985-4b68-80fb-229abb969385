<%*
// Simplified YTranscript to SRT converter - Manual workflow
try {
    new Notice("Starting YTranscript to SRT conversion...", 2000);
    
    // Get YouTube URL from user
    const videoUrl = await tp.system.prompt("Enter YouTube URL:");
    if (!videoUrl || !videoUrl.trim()) {
        new Notice("No URL provided. Cancelling.", 3000);
        tR += "No URL provided.";
        return;
    }
    
    // Extract video ID and create standard URL
    const videoIdMatch = videoUrl.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/);
    if (!videoIdMatch) {
        new Notice("Invalid YouTube URL format.", 5000);
        tR += "Invalid YouTube URL format.";
        return;
    }
    
    const videoId = videoIdMatch[1];
    const standardUrl = `https://www.youtube.com/watch?v=${videoId}`;
    
    // Check if current note has YTranscript content
    const fileContent = tp.file.content || "";
    const hasTranscript = fileContent.includes('[') && fileContent.includes('youtube.com');
    
    if (!hasTranscript) {
        new Notice("No YTranscript content found. Please run YTranscript first!", 6000);
        tR += `# YTranscript to SRT Converter

**Step 1:** Please run one of these YTranscript commands first:
- **YTranscript: Get YouTube transcript from url prompt**
- **YTranscript: Insert YouTube transcript**

**Video URL:** ${standardUrl}

**Step 2:** After getting the transcript, run this template again to convert it to SRT format.

---

**Manual Steps:**
1. Copy this URL: ${standardUrl}
2. Run YTranscript command (Ctrl+P → search "ytranscript")
3. Paste the URL when prompted
4. Wait for transcript to appear in this note
5. Run this template again
`;
        return;
    }
    
    new Notice("Found YTranscript content, processing...", 2000);
    
    // Parse YTranscript format
    const transcriptRegex = /\[(\d{1,2}:\d{2}(?::\d{2})?)\]\((https:\/\/www\.youtube\.com\/watch\?v=.*?)\) ?(.*?)(?=\n\[|\n*$)/g;
    const segments = [];
    
    let match;
    while ((match = transcriptRegex.exec(fileContent)) !== null) {
        const text = match[3]
            .replace(/\u00a0/g, ' ')  // Replace non-breaking spaces
            .replace(/\s+/g, ' ')     // Normalize spaces
            .trim();
            
        if (text) {
            segments.push({
                time: match[1],
                text: text
            });
        }
    }
    
    if (segments.length === 0) {
        new Notice("No valid transcript segments found!", 5000);
        tR += `# No Valid Transcript Found

The note contains some content but no valid YTranscript segments were detected.

**Expected format:**
\`[0:00](https://www.youtube.com/watch?v=VIDEO_ID) Transcript text here\`

**Current content preview:**
\`\`\`
${fileContent.substring(0, 300)}...
\`\`\`
`;
        return;
    }
    
    new Notice(`Processing ${segments.length} segments...`, 2000);
    
    // Generate SRT content
    let srtContent = '';
    for (let i = 0; i < segments.length; i++) {
        const segment = segments[i];
        const nextSegment = segments[i + 1];
        
        // Convert time to SRT format
        const timeParts = segment.time.split(':').map(Number);
        let startSeconds = 0;
        if (timeParts.length === 3) {
            startSeconds = timeParts[0] * 3600 + timeParts[1] * 60 + timeParts[2];
        } else {
            startSeconds = timeParts[0] * 60 + timeParts[1];
        }
        
        // Calculate end time
        let endSeconds;
        if (nextSegment) {
            const nextTimeParts = nextSegment.time.split(':').map(Number);
            if (nextTimeParts.length === 3) {
                endSeconds = nextTimeParts[0] * 3600 + nextTimeParts[1] * 60 + nextTimeParts[2] - 0.1;
            } else {
                endSeconds = nextTimeParts[0] * 60 + nextTimeParts[1] - 0.1;
            }
        } else {
            endSeconds = startSeconds + 10; // 10 seconds for last segment
        }
        
        // Format times for SRT
        const formatTime = (seconds) => {
            const h = Math.floor(seconds / 3600);
            const m = Math.floor((seconds % 3600) / 60);
            const s = Math.floor(seconds % 60);
            return `${h.toString().padStart(2, '0')}:${m.toString().padStart(2, '0')}:${s.toString().padStart(2, '0')},000`;
        };
        
        srtContent += `${i + 1}\n`;
        srtContent += `${formatTime(startSeconds)} --> ${formatTime(endSeconds)}\n`;
        srtContent += `${segment.text}\n\n`;
    }
    
    // Create file paths
    const path = require('path');
    const fs = require('fs').promises;
    const crypto = require('crypto');
    
    const vaultPath = tp.app.vault.adapter.basePath;
    const subtitlesDir = path.join(vaultPath, 'z_subtitles');
    const mediaLibDir = path.join(vaultPath, 'media-lib');
    
    // Create directories
    await fs.mkdir(subtitlesDir, { recursive: true });
    await fs.mkdir(mediaLibDir, { recursive: true });
    
    // Generate unique filenames
    const uid = crypto.randomBytes(13).toString('base64').replace(/[^a-z0-9]/gi, '').substring(0, 26);
    const shortId = crypto.randomBytes(2).toString('hex');
    const srtFileName = `${uid}.${shortId}.en.srt`;
    const mediaFileName = `url-autogen-${crypto.randomBytes(4).toString('hex')}.md`;
    
    // Write SRT file
    const srtPath = path.join(subtitlesDir, srtFileName);
    await fs.writeFile(srtPath, srtContent, 'utf8');
    
    // Create media note
    const mediaContent = `---
mx-uid: ${uid}
media: ${standardUrl}
subtitles:
  - "[[${srtFileName}#lang=en&label=English (Punctuated)]]"
---
Auto-generated media note for Media Extended plugin.

Video: ${standardUrl}
Generated: ${new Date().toISOString()}
Segments: ${segments.length}`;
    
    const mediaPath = path.join(mediaLibDir, mediaFileName);
    await fs.writeFile(mediaPath, mediaContent, 'utf8');
    
    new Notice(`✅ Success! Created SRT with ${segments.length} segments`, 4000);
    
    // Return result
    tR += `---
video: ${standardUrl}
transcript: "[[${srtFileName}]]"
media-note: "[[${mediaFileName}]]"
processed: ${new Date().toISOString()}
segments: ${segments.length}
---

# Video Transcript Processed

**Video:** [${standardUrl}](${standardUrl})
**SRT File:** [[${srtFileName}]]
**Media Note:** [[${mediaFileName}]]

✅ **Successfully processed ${segments.length} transcript segments**

> Use the Media Extended plugin to play the video with synchronized subtitles.

## First 3 Segments Preview:
${segments.slice(0, 3).map(s => `**${s.time}:** ${s.text}`).join('\n\n')}

${segments.length > 3 ? `\n_... and ${segments.length - 3} more segments_` : ''}
`;

} catch (error) {
    new Notice(`Error: ${error.message}`, 8000);
    console.error('YTranscript to SRT Error:', error);
    
    tR += `# Conversion Error

**Error:** ${error.message}

**Stack:**
\`\`\`
${error.stack}
\`\`\`

**Troubleshooting:**
1. Make sure YTranscript content is in the current note
2. Check that the transcript format is correct
3. Verify file system permissions for creating SRT files
`;
}
%>
