<%*
// Alternative method using exec with full command string
const browserPath = "C:\\zen\\zen-01-portable\\app\\win\\zen.exe";
const profilePath = "C:\\zen\\zen-01-portable\\data\\profile1";
const workingDir = "C:\\zen\\zen-01-portable\\app\\win";
const url = "https://google.com";

// Build complete command string
const fullCommand = `cd /d "${workingDir}" && "${browserPath}" -profile "${profilePath}" -no-remote "${url}"`;

try {
    if (typeof require !== 'undefined') {
        const { exec } = require('child_process');

        console.log("Executing command:", fullCommand);

        exec(fullCommand, {
            cwd: workingDir
        }, (error, stdout, stderr) => {
            if (error) {
                console.error('Execution error:', error);
                new Notice("Launch failed: " + error.message);
            } else {
                console.log('Browser launched successfully');
                if (stdout) console.log('stdout:', stdout);
                if (stderr) console.log('stderr:', stderr);
            }
        });

        new Notice("Launching Zen Browser with Profile1...");

    } else {
        new Notice("Node.js not available");
        console.log("Command to run:", fullCommand);
    }

} catch (error) {
    console.error("Error:", error);
    new Notice("Error: " + error.message);
}
%>