<%*
// Template to process existing YTranscript content in the current note
try {
    new Notice("Processing existing YTranscript content...", 2000);
    
    const fileContent = tp.file.content || "";
    
    // Check if the note contains YTranscript format
    const hasYTranscript = fileContent.includes('[') && fileContent.includes('youtube.com');
    
    if (!hasYTranscript) {
        new Notice("No YTranscript content found in current note!", 5000);
        tR += `# No YTranscript Content Found

This template processes existing YTranscript content, but none was found in the current note.

**To use this template:**
1. First run a YTranscript command to fetch transcript content
2. Then run this template to process it into SRT format

**Expected YTranscript format:**
\`\`\`
[0:00](https://www.youtube.com/watch?v=VIDEO_ID) Transcript text here
[0:15](https://www.youtube.com/watch?v=VIDEO_ID) More transcript text
\`\`\`
`;
        return;
    }
    
    // Load and run the converter
    const converter = require(tp.app.vault.adapter.basePath + '/_templates/_scripts/converter_v3.js');
    
    // Create a mock URL prompt that returns empty (so it uses existing content)
    const originalPrompt = tp.system.prompt;
    tp.system.prompt = async (message) => {
        if (message.includes("YouTube URL")) {
            // Extract URL from existing content
            const urlMatch = fileContent.match(/https:\/\/www\.youtube\.com\/watch\?v=([^&\n?#)]+)/);
            if (urlMatch) {
                return urlMatch[0];
            }
            return "https://www.youtube.com/watch?v=dummy"; // Fallback
        }
        return originalPrompt(message);
    };
    
    const result = await converter(tp);
    
    // Restore original prompt function
    tp.system.prompt = originalPrompt;
    
    if (result) {
        tR += result;
    } else {
        tR += "Processing completed but no result returned.";
    }
    
} catch (error) {
    new Notice(`Error processing YTranscript content: ${error.message}`, 8000);
    console.error('YTranscript Processing Error:', error);
    
    tR += `# Processing Error

**Error Message:** ${error.message}

**Stack Trace:**
\`\`\`
${error.stack || 'No stack trace available'}
\`\`\`

**Current Note Content Preview:**
\`\`\`
${(tp.file.content || '').substring(0, 500)}...
\`\`\`
`;
}
%>
