# Regex Parsing Test

## Sample Content:
```
[0:00](https://www.youtube.com/watch?v=Xu1X-J-r5Xk) Welcome to this video about testing.
[0:15](https://www.youtube.com/watch?v=Xu1X-J-r5Xk) This is the second segment with some text.
[0:30](https://www.youtube.com/watch?v=Xu1X-J-r5Xk) Here's another segment to test parsing.
[1:00](https://www.youtube.com/watch?v=Xu1X-J-r5Xk) Final test segment with more content.
```

## Parsing Results:

### Using matchAll (4 matches found):

**Match 1:**
- Time: 0:00
- URL: https://www.youtube.com/watch?v=Xu1X-J-r5Xk
- Text: "Welcome to this video about testing."

**Match 2:**
- Time: 0:15
- URL: https://www.youtube.com/watch?v=Xu1X-J-r5Xk
- Text: "This is the second segment with some text."

**Match 3:**
- Time: 0:30
- URL: https://www.youtube.com/watch?v=Xu1X-J-r5Xk
- Text: "Here's another segment to test parsing."

**Match 4:**
- Time: 1:00
- URL: https://www.youtube.com/watch?v=Xu1X-J-r5Xk
- Text: "Final test segment with more content."

## Testing with Current Note Content:

Found 45 segments in current note.

### First 3 segments:
**1.** [00:00] "[MUSIC PLAYING]"
**2.** [00:03] "ALICE LISAK: Hi."
**3.** [00:11] "I'm Alice. LUCAS GONZALEZ: I'm Lucas."


[00:00](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=0) [MUSIC PLAYING]
[00:03](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=3) ALICE LISAK: Hi.
[00:11](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=11) I'm Alice. LUCAS GONZALEZ: I'm Lucas.
[00:14](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=14) We're product managers
at Google DeepMind. ALICE LISAK: And today, we are
incredibly excited to introduce
[00:20](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=20) EmbeddingGemma, our
state-of-the-art embedding model designed for mobile-first AI.
[00:00](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=0) LUCAS GONZALEZ: EmbeddingGemma
is a 300-million-parameter text
[00:29](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=29) embedding model designed to
power generative AI experiences directly on your hardware.
[00:36](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=36) Embeddings are numerical
representations of data. This model transforms text
like messages, emails, or notes
[00:45](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=45) into a vector of
numbers to represent meaning in a
high-dimensional space
[00:49](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=49) that a generative model can
then use for downstream tasks. ALICE LISAK: EmbeddingGemma
is small, fast, and efficient.
[00:56](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=56) Thanks to
quantization-aware training, you can run the model with as
little as 300 megabytes of RAM
[01:03](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=63) while preserving
state-of-the-art quality. It generates embeddings
of 768 dimensions,
[01:10](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=70) but thanks to Matryoshka
Representation Learning, you can customize the
model's output dimensions
[01:15](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=75) and go down to 128.
[01:18](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=78) LUCAS GONZALEZ: Based on the
same technology and research that powers our Gemini
embedding models,
[01:24](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=84) EmbeddingGemma brings that
state-of-the-art capability in a smaller and more
lightweight model.
[01:30](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=90) Think high-quality semantic
search, fast and relevant information retrieval, or
customized classification
[01:37](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=97) and clustering, just to
name a few opportunities. ALICE LISAK: EmbeddingGemma
achieves the best score
[01:43](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=103) on the comprehensive massive
text embedding benchmark for models under 500 million
parameters, the gold standard
[01:51](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=111) for text embedding evaluation. Trained across
100-plus languages,
[01:56](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=116) EmbeddingGemma brings proven
performance to instantly connect with diverse and
global audiences.
[02:03](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=123) LUCAS GONZALEZ: We've
engineered EmbeddingGemma specifically for
on-device performance
[02:07](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=127) to ensure efficient
computations and minimal memory footprint, even on
resource-constrained hardware.
[02:14](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=134) EmbeddingGemma facilitates
on-device embedding of local documents, so
sensitive user data never
[02:20](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=140) leaves the device. And because it works
offline, it means
[02:24](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=144) frontier search and
retrieval features work regardless of connectivity.
[00:00](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=0) ALICE LISAK: Together with our
generative models like Gemma 3n,
[02:32](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=152) you can build powerful,
mobile-first generative AI experiences and efficient,
retrieval-augmented generation
[02:40](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=160) pipelines. This means your applications
can now leverage user context
[02:44](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=164) from data to provide more
personalized and helpful responses, such as
understanding that you
[02:50](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=170) need your carpenter's number for
help with damaged floorboards.
[02:55](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=175) Here's an example of what
EmbeddingGemma can power. What you are seeing
is how a user
[03:00](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=180) can utilize EmbeddingGemma
to query previously opened articles or other web pages.
[03:05](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=185) The model embeds each page
as it's opened in real time. Then, with a browser extension
that uses EmbeddingGemma,
[03:13](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=193) the user can ask a question
to retrieve the contextually relevant articles.
[03:17](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=197) And because the embeddings
are created on-device, all this is happening without
leaving the user's hardware.
[00:00](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=0) LUCAS GONZALEZ:
And it's designed
[03:24](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=204) with customization in mind. Fine-tune EmbeddingGemma
for your domain
[03:29](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=209) or in a particular language. It works across popular tools
and platforms such as Hugging
[03:34](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=214) Face and Kaggle. Check out our notebook examples,
part of the Gemma Cookbook,
[03:39](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=219) to get started.
[03:41](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=221) ALICE LISAK: Our next generation
of on-device embedding models is here, and it's
open for everyone.
[03:47](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=227) It's small, fast, and efficient. Download EmbeddingGemma and
get started building right now.
[03:53](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=233) LUCAS GONZALEZ: You can find
links in the description below. We can't wait to see what
EmbeddingGemma unlocks for you.
[03:59](https://www.youtube.com/watch?v=Xu1X-J-r5Xk&t=239) [MUSIC PLAYING]