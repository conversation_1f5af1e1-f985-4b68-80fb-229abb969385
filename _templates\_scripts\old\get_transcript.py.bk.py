# C:\ObsidianVault\Notes-Core\_templates\_scripts\get_transcript.py
import sys
import subprocess
import os
import pathlib
import json
import tempfile
import shutil

def get_transcript_with_yt_dlp(url):
    """
    Enhanced version with multiple fallback methods and better debugging
    """
    try:
        python_executable = sys.executable
        script_dir = pathlib.Path(__file__).parent.resolve()
        
        cookie_file_path = script_dir / 'youtube-cookies.txt'
        headers_file_path = script_dir / 'youtube-headers-clean.txt'

        if not cookie_file_path.exists():
            return "Error: Could not find 'youtube-cookies.txt'."
        if not headers_file_path.exists():
            return "Error: Could not find 'youtube-headers-clean.txt'."

        # Build header arguments
        header_args = []
        with open(headers_file_path, 'r', encoding='utf-8') as f:
            for line in f:
                stripped_line = line.strip()
                if stripped_line:
                    header_args.append('--add-header')
                    header_args.append(stripped_line)

        if not header_args:
            return "Error: 'youtube-headers-clean.txt' is empty."

        # Create a temporary directory for downloads
        with tempfile.TemporaryDirectory() as temp_dir:
            # Method 1: Try to get subtitles with standard approach
            base_command = [
                python_executable,
                '-m', 'yt_dlp',
                '--ignore-config',
                '--cookies', str(cookie_file_path),
                '--no-warnings',
                '--write-subs',
                '--write-auto-subs',
                '--sub-lang', 'en.*,en',  # Try multiple English variants
                '--skip-download',
                '--sub-format', 'srt/vtt/best',  # Try multiple formats
                '--convert-subs', 'srt',  # Convert to SRT if needed
                '-o', f'{temp_dir}/%(title)s.%(ext)s',
                '--verbose',  # Add verbose output for debugging
                url
            ] + header_args
            
            startupinfo = None
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW

            # First attempt with full authentication
            result = subprocess.run(base_command, capture_output=True, text=True, encoding='utf-8', startupinfo=startupinfo)
            
            # Check if any subtitle files were created
            srt_files = list(pathlib.Path(temp_dir).glob('*.srt'))
            vtt_files = list(pathlib.Path(temp_dir).glob('*.vtt'))
            
            if srt_files:
                with open(srt_files[0], 'r', encoding='utf-8') as f:
                    return f.read()
            elif vtt_files:
                # Convert VTT to SRT format if needed
                with open(vtt_files[0], 'r', encoding='utf-8') as f:
                    vtt_content = f.read()
                    return convert_vtt_to_srt(vtt_content)
            
            # Method 2: Try without cookies (sometimes works better for public videos)
            if not srt_files and not vtt_files:
                simple_command = [
                    python_executable,
                    '-m', 'yt_dlp',
                    '--write-auto-subs',
                    '--sub-lang', 'en',
                    '--skip-download',
                    '--sub-format', 'srt/vtt/best',
                    '--convert-subs', 'srt',
                    '-o', f'{temp_dir}/%(title)s_nocookie.%(ext)s',
                    url
                ]
                
                result2 = subprocess.run(simple_command, capture_output=True, text=True, encoding='utf-8', startupinfo=startupinfo)
                
                # Check again for subtitle files
                srt_files = list(pathlib.Path(temp_dir).glob('*.srt'))
                vtt_files = list(pathlib.Path(temp_dir).glob('*.vtt'))
                
                if srt_files:
                    with open(srt_files[0], 'r', encoding='utf-8') as f:
                        return f.read()
                elif vtt_files:
                    with open(vtt_files[0], 'r', encoding='utf-8') as f:
                        vtt_content = f.read()
                        return convert_vtt_to_srt(vtt_content)
            
            # Method 3: Get video info and check what's available
            info_command = [
                python_executable,
                '-m', 'yt_dlp',
                '--dump-json',
                '--no-warnings',
                url
            ]
            
            info_result = subprocess.run(info_command, capture_output=True, text=True, encoding='utf-8', startupinfo=startupinfo)
            
            if info_result.stdout:
                try:
                    video_info = json.loads(info_result.stdout)
                    
                    # Check if subtitles are available
                    if 'subtitles' in video_info:
                        available_langs = list(video_info['subtitles'].keys())
                        if available_langs:
                            return f"Error: Subtitles available for languages: {', '.join(available_langs)}, but couldn't download. Try updating yt-dlp."
                    
                    if 'automatic_captions' in video_info:
                        available_langs = list(video_info['automatic_captions'].keys())
                        if available_langs:
                            return f"Error: Auto-captions available for languages: {', '.join(available_langs)}, but couldn't download. Try updating yt-dlp."
                    
                    return "Error: No subtitles or captions available for this video."
                except json.JSONDecodeError:
                    pass
            
            # If we got here, no subtitles were found
            error_msg = "Error: yt-dlp ran successfully but found no transcript content.\n"
            if result.stderr:
                error_msg += f"Debug info: {result.stderr[:500]}"
            return error_msg
            
    except subprocess.CalledProcessError as e:
        return f"Error from yt-dlp: {e.stderr.strip() if e.stderr else str(e)}"
    except Exception as e:
        return f"An unexpected error occurred: {str(e)}"

def convert_vtt_to_srt(vtt_content):
    """
    Simple VTT to SRT converter
    """
    lines = vtt_content.split('\n')
    srt_lines = []
    counter = 1
    
    i = 0
    while i < len(lines):
        line = lines[i].strip()
        
        # Skip WEBVTT header and empty lines
        if line.startswith('WEBVTT') or not line:
            i += 1
            continue
        
        # Check if this is a timestamp line
        if ' --> ' in line:
            # Add subtitle number
            srt_lines.append(str(counter))
            counter += 1
            
            # Convert timestamp format (remove .vtt styling if present)
            timestamp = line.split(' ')[0] + ' --> ' + line.split(' --> ')[1].split(' ')[0]
            timestamp = timestamp.replace('.', ',')  # SRT uses comma for milliseconds
            srt_lines.append(timestamp)
            
            # Add the subtitle text
            i += 1
            text_lines = []
            while i < len(lines) and lines[i].strip() and ' --> ' not in lines[i]:
                text_lines.append(lines[i].strip())
                i += 1
            
            if text_lines:
                srt_lines.append('\n'.join(text_lines))
                srt_lines.append('')  # Empty line between subtitles
        else:
            i += 1
    
    return '\n'.join(srt_lines)

if __name__ == "__main__":
    if len(sys.argv) < 2: 
        print("Error: Please provide a YouTube URL.", file=sys.stderr)
        sys.exit(1)
    
    srt_result = get_transcript_with_yt_dlp(sys.argv[1])
    
    if srt_result.strip().startswith("Error:"): 
        print(srt_result, file=sys.stderr)
        sys.exit(1)
    else: 
        print(srt_result)