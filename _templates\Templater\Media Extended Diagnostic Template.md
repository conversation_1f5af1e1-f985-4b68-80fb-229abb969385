
<%*
// Diagnostic script to understand Media Extended setup
const path = require('path');
const fs = require('fs').promises;

const vaultPath = tp.app.vault.adapter.basePath;
let output = "# Media Extended Diagnostic Report\n\n";

// Check if Media Extended is installed
const pluginPath = path.join(vaultPath, '.obsidian/plugins/media-extended');
try {
    await fs.access(pluginPath);
    output += "✅ Media Extended plugin folder found\n\n";
    
    // Check for data.json
    const dataPath = path.join(pluginPath, 'data.json');
    try {
        const dataContent = await fs.readFile(dataPath, 'utf8');
        const data = JSON.parse(dataContent);
        
        output += "## Plugin Configuration\n";
        output += "```json\n";
        output += JSON.stringify(data.settings || {}, null, 2).substring(0, 500);
        output += "\n```\n\n";
        
        // Check for existing media links
        if (data.mediaLinks && Object.keys(data.mediaLinks).length > 0) {
            output += `## Existing Media Links: ${Object.keys(data.mediaLinks).length}\n`;
            const firstThree = Object.entries(data.mediaLinks).slice(0, 3);
            firstThree.forEach(([url, config]) => {
                output += `- ${url.substring(0, 50)}...\n`;
                if (config.tracks) {
                    output += `  - Tracks: ${config.tracks.length}\n`;
                }
            });
            output += "\n";
        } else {
            output += "## No existing media links found\n\n";
        }
        
    } catch (e) {
        output += `⚠️ Could not read data.json: ${e.message}\n\n`;
    }
    
    // Check for subtitles folder
    const subtitlesPath = path.join(pluginPath, 'subtitles');
    try {
        await fs.access(subtitlesPath);
        const files = await fs.readdir(subtitlesPath);
        output += `## Subtitles Folder\n`;
        output += `Found ${files.length} subtitle files\n`;
        if (files.length > 0) {
            output += "Recent files:\n";
            files.slice(-5).forEach(f => {
                output += `- ${f}\n`;
            });
        }
        output += "\n";
    } catch (e) {
        output += "⚠️ No subtitles folder found\n\n";
    }
    
} catch (e) {
    output += "❌ Media Extended plugin not found or not accessible\n\n";
}

// Check for media-extended folder in vault
const vaultMediaPath = path.join(vaultPath, 'media-extended');
try {
    await fs.access(vaultMediaPath);
    const files = await fs.readdir(vaultMediaPath);
    output += `## Vault media-extended folder\n`;
    output += `Found ${files.length} items\n\n`;
} catch (e) {
    output += "ℹ️ No media-extended folder in vault root\n\n";
}

// Test YouTube URL formats
output += "## Test URLs\n";
output += "Try these different formats in your note:\n\n";

const testUrl = "https://www.youtube.com/watch?v=490TVXsY4Dk";
output += "### 1. Direct URL\n";
output += `${testUrl}\n\n`;

output += "### 2. Markdown link\n";
output += `[Test Video](${testUrl})\n\n`;

output += "### 3. Embed syntax\n";
output += `![](${testUrl})\n\n`;

output += "### 4. Media-view (if supported)\n";
output += "\\`\\`\\`media-view\n";
output += `- url: ${testUrl}\n`;
output += "\\`\\`\\`\n\n";

tR += output;
%>