// C:\ObsidianVault\Notes-Core\_templates\_scripts\converter_v2.js

const { exec } = require('child_process');
const util = require('util');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');

// Helper function to generate unique IDs
function generateMxId(length = 26) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Helper function to format MM:SS or HH:MM:SS into HH:MM:SS,mmm
function formatSrtTime(timeStr) {
    const parts = timeStr.split(':').map(Number);
    let hours = 0, minutes, seconds;
    
    if (parts.length === 3) {
        [hours, minutes, seconds] = parts;
    } else {
        [minutes, seconds] = parts;
    }
    
    const hh = String(hours).padStart(2, '0');
    const mm = String(minutes).padStart(2, '0');
    const ss = String(seconds).padStart(2, '0');
    
    return `${hh}:${mm}:${ss},000`;
}

async function processYTranscript(tp) {
    new Notice("Starting YTranscript V2 Processor...", 3000);

    const fileContent = tp.file.content;
    if (!fileContent || fileContent.trim().length === 0) {
        new Notice("Error: Current note is empty.", 5000);
        return "";
    }

    const regex = /\[(\d{1,2}:\d{2}(?::\d{2})?)\]\((https:\/\/www.youtube.com\/watch\?v=.*?)\) ?([\s\S]*?)(?=\[\d{1,2}:\d{2}(?::\d{2})?\]|$)/g;
    
    const parsedData = [];
    let match;
    let baseUrl = null;

    while ((match = regex.exec(fileContent)) !== null) {
        if (!baseUrl) {
            const url = new URL(match[2]);
            baseUrl = `${url.origin}${url.pathname}?v=${url.searchParams.get('v')}`;
        }
        parsedData.push({
            startTime: match[1],
            text: match[3].trim().replace(/\n/g, ' ')
        });
    }

    if (parsedData.length === 0) {
        new Notice("Error: Could not find valid YTranscript data in the note.", 6000);
        return "";
    }
    
    // --- SRT Conversion Logic ---
    let srtContent = '';
    for (let i = 0; i < parsedData.length; i++) {
        const entry = parsedData[i];
        const nextEntry = parsedData[i + 1];

        const srtIndex = i + 1;
        const srtStartTime = formatSrtTime(entry.startTime);
        
        let srtEndTime;
        if (nextEntry) {
            srtEndTime = formatSrtTime(nextEntry.startTime);
        } else {
            // --- THIS IS THE FIX ---
            // Robustly calculate the end time for the VERY LAST line of the transcript.
            const timeParts = entry.startTime.split(':').map(Number);
            let totalSeconds = 0;
            if (timeParts.length === 3) { // Handles HH:MM:SS
                totalSeconds = timeParts[0] * 3600 + timeParts[1] * 60 + timeParts[2];
            } else { // Handles MM:SS
                totalSeconds = timeParts[0] * 60 + timeParts[1];
            }
            totalSeconds += 10; // Add a safe 10-second duration
            
            const endHours = Math.floor(totalSeconds / 3600);
            const endMinutes = Math.floor((totalSeconds % 3600) / 60);
            const endSeconds = totalSeconds % 60;
            
            srtEndTime = `${String(endHours).padStart(2, '0')}:${String(endMinutes).padStart(2, '0')}:${String(endSeconds).padStart(2, '0')},000`;
            // --- END OF FIX ---
        }

        srtContent += `${srtIndex}\n`;
        srtContent += `${srtStartTime} --> ${srtEndTime}\n`;
        srtContent += `${entry.text}\n\n`;
    }

    const vaultPath = tp.app.vault.adapter.basePath;
    const subtitlesDir = path.join(vaultPath, 'z_subtitles');
    const mediaLibDir = path.join(vaultPath, 'media-lib');

    try {
        await fs.mkdir(subtitlesDir, { recursive: true });
        await fs.mkdir(mediaLibDir, { recursive: true });
    } catch (err) {}

    const mediaNoteUID = generateMxId();
    const shortId = crypto.randomBytes(3).toString('hex').substring(0, 4);
    const mediaNoteFileName = `url-autogen-${generateMxId(8)}.md`;
    const srtFileName = `${mediaNoteUID}.${shortId}.en.srt`;
    const srtFilePath = path.join(subtitlesDir, srtFileName);
    
    await fs.writeFile(srtFilePath, srtContent, 'utf8');

    const mediaNoteContent = `---
mx-uid: ${mediaNoteUID}
media: ${baseUrl}
subtitles:
  - "[[${srtFileName}#lang=en&label=English (Punctuated)]]"
---
This is an auto-generated media note for the Media Extended plugin.`;

    const mediaNotePath = path.join(mediaLibDir, mediaNoteFileName);
    await fs.writeFile(mediaNotePath, mediaNoteContent, 'utf8');
    new Notice(`Punctuated transcript linked successfully!`, 4000);

    // Replace the raw YTranscript text with the final, clean links.
    return `
---
Video: ${baseUrl}
Transcript: [[${srtFileName}]]
 
---

`;
}

module.exports = processYTranscript;
