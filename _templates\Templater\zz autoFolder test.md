<%*
let qcFileName = await tp.system.prompt("Note Title")
let titleName = tp.date.now("YYYY MMDD") + " - " + qcFileName
await tp.file.rename(titleName)
let baseFolder = "/3. Meeting Notes/"
let year = tp.date.now('YYYY') 
let month = tp.date.now('MM-MMM') 
let newFolder = `${baseFolder}${year}/${month}/` 
await tp.file.move(newFolder + titleName);
-%>
---
bcreation_date: <% tp. file. creation_date() %>
type: aaa
tags: notes/meeting
status: draft 
summary: ""

---
Date: <% tp.date.now("YYYY MMDD dddd") %>

XXX: <% tp.file.cursor() %>