// Debug script to find YTranscript commands
function debugYTranscriptCommands(tp) {
    const commands = tp.app.commands.listCommands();
    const ytCommands = commands.filter(cmd => 
        cmd.id.toLowerCase().includes('ytranscript') ||
        cmd.name.toLowerCase().includes('youtube') ||
        cmd.name.toLowerCase().includes('transcript')
    );
    
    console.log("=== YTranscript Commands Debug ===");
    ytCommands.forEach(cmd => {
        console.log(`ID: ${cmd.id}`);
        console.log(`Name: ${cmd.name}`);
        console.log("---");
    });
    
    return ytCommands.map(c => `**${c.name}**\nID: \`${c.id}\`\n`).join('\n');
}

module.exports = debugYTranscriptCommands;