<%*
// Use Node.js's built-in 'path' module for reliable path handling
const path = require('path');

// 1. Get the absolute path to the vault's root folder. This is more reliable.
const vaultPath = tp.app.vault.adapter.basePath;

// 2. Define the relative path to our user script
const scriptRelativePath = '_templates/_scripts/converter.js';

// 3. Join them to create a full, absolute path that 'require' can use
const fullScriptPath = path.join(vaultPath, scriptRelativePath);

// 4. Require the script using the absolute path
const converter = require(fullScriptPath);

// 5. Prompt the user for the YouTube URL
const youtubeUrl = await tp.system.prompt("Enter YouTube URL");

// 6. Process the URL and get the final output block
const output = await converter(tp, youtubeUrl);

// 7. Insert the output into the note
if (output) {
  tR += output;
}
%>