<%*
// Step-by-step test of V3 converter automation
try {
    new Notice("🔍 Testing V3 Converter Step by Step...", 3000);
    
    let output = `# V3 Converter Step-by-Step Test

## Step 1: Check YTranscript Commands
`;
    
    // Check available YTranscript commands
    const commands = tp.app.commands.listCommands();
    const ytCommands = commands.filter(cmd => cmd.id.includes('ytranscript'));
    
    output += `Found ${ytCommands.length} YTranscript commands:
`;
    ytCommands.forEach(cmd => {
        output += `- **${cmd.name}** (\`${cmd.id}\`)
`;
    });
    
    output += `
## Step 2: Get YouTube URL
`;
    
    const videoUrl = await tp.system.prompt("Enter YouTube URL for testing:");
    if (!videoUrl || !videoUrl.trim()) {
        output += "❌ No URL provided. Test cancelled.";
        tR += output;
        return;
    }
    
    output += `URL: ${videoUrl}

## Step 3: Test YTranscript Command Execution
`;
    
    // Test the YTranscript command execution logic
    try {
        // Clear note and add URL
        await tp.app.vault.modify(tp.file, videoUrl);
        output += `✅ Note cleared and URL added

`;
        
        // Find YTranscript command
        const commandsToTry = [
            'ytranscript:transcript-from-text',
            'ytranscript:insert-youtube-transcript',
            'ytranscript:transcript-from-prompt'
        ];
        
        let ytranscriptCommand = null;
        for (const cmdId of commandsToTry) {
            ytranscriptCommand = commands.find(cmd => cmd.id === cmdId);
            if (ytranscriptCommand) {
                output += `✅ Found command: ${ytranscriptCommand.name} (${ytranscriptCommand.id})

`;
                break;
            }
        }
        
        if (!ytranscriptCommand) {
            output += `❌ No suitable YTranscript command found

`;
            tR += output;
            return;
        }
        
        // Try to execute the command
        output += `## Step 4: Execute YTranscript Command

Executing: ${ytranscriptCommand.name}...

`;
        
        // Select URL text if needed
        const editor = tp.app.workspace.activeLeaf?.view?.editor;
        if (ytranscriptCommand.id === 'ytranscript:transcript-from-text' && editor) {
            editor.setSelection({line: 0, ch: 0}, {line: 0, ch: videoUrl.length});
            output += `✅ URL text selected for transcript-from-text command

`;
        }
        
        // Execute command
        await tp.app.commands.executeCommandById(ytranscriptCommand.id);
        output += `✅ Command executed successfully

## Step 5: Wait for YTranscript to Complete

Checking for transcript content...

`;
        
        // Wait and check for content
        let attempts = 0;
        const maxAttempts = 10;
        let transcriptFound = false;
        
        while (attempts < maxAttempts && !transcriptFound) {
            await new Promise(resolve => setTimeout(resolve, 1000));
            attempts++;
            
            const currentContent = tp.file.content || "";
            
            if (currentContent.includes('[') && 
                currentContent.includes('youtube.com') && 
                currentContent.length > videoUrl.length + 100) {
                
                transcriptFound = true;
                output += `✅ Transcript content detected after ${attempts} seconds!

Content length: ${currentContent.length} characters
Contains timestamp markers: ${(currentContent.match(/\[\d{1,2}:\d{2}/g) || []).length}

## Step 6: Parse Transcript Content

`;
                
                // Test parsing
                const lines = currentContent.split('\n');
                let parsedSegments = 0;
                
                for (const line of lines) {
                    const match = line.match(/\[(\d{1,2}:\d{2}(?::\d{2})?)\]\((https:\/\/www\.youtube\.com\/watch\?v=[^)]+)\)\s*(.*)/);
                    if (match && match[3].trim()) {
                        parsedSegments++;
                    }
                }
                
                output += `✅ Successfully parsed ${parsedSegments} transcript segments

## Step 7: Test Complete!

The V3 converter automation is working. You can now:
1. Use the "Auto YTranscript V3" template for full automation
2. Or use the "Process YTranscript V3" template for manual workflow

**Next Steps:**
- Try the full V3 converter with: "Auto YTranscript V3" template
- The transcript content is now in this note and ready for processing
`;
                
                break;
            }
            
            if (attempts === 5) {
                output += `⏳ Still waiting... (${attempts}/10 seconds)

`;
            }
        }
        
        if (!transcriptFound) {
            output += `❌ No transcript content appeared after ${maxAttempts} seconds

**Possible Issues:**
1. YTranscript command may need manual interaction
2. Network issues preventing transcript fetch
3. Video may not have available transcripts
4. YTranscript plugin may need different approach

**Manual Alternative:**
1. Run YTranscript command manually
2. Wait for transcript to appear
3. Then use "Process Existing YTranscript" template
`;
        }
        
    } catch (cmdError) {
        output += `❌ Command execution failed: ${cmdError.message}

**Error Details:**
\`\`\`
${cmdError.stack || 'No stack trace'}
\`\`\`
`;
    }
    
    tR += output;
    
} catch (error) {
    new Notice(`Test error: ${error.message}`, 5000);
    tR += `# Test Error

**Error:** ${error.message}

**Stack:**
\`\`\`
${error.stack}
\`\`\`
`;
}
%>
