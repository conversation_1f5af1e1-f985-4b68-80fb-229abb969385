<%*
// Debug template to find and test YTranscript commands
try {
    new Notice("Scanning for YTranscript commands...", 2000);
    
    const commands = tp.app.commands.listCommands();
    const allCommands = commands.map(cmd => ({
        id: cmd.id,
        name: cmd.name
    }));
    
    // Filter for YTranscript related commands
    const ytCommands = commands.filter(cmd => 
        cmd.id.toLowerCase().includes('ytranscript') ||
        cmd.name.toLowerCase().includes('youtube') ||
        cmd.name.toLowerCase().includes('transcript')
    );
    
    let output = `# YTranscript Command Debug Report

**Total Commands Found:** ${commands.length}
**YTranscript Related Commands:** ${ytCommands.length}

## YTranscript Commands:

`;

    if (ytCommands.length > 0) {
        ytCommands.forEach(cmd => {
            output += `### ${cmd.name}
- **ID:** \`${cmd.id}\`
- **Callback Available:** ${cmd.callback ? 'Yes' : 'No'}
- **Check Callback Available:** ${cmd.checkCallback ? 'Yes' : 'No'}

`;
        });
    } else {
        output += `❌ **No YTranscript commands found!**

This could mean:
1. YTranscript plugin is not installed
2. YTranscript plugin is not enabled
3. YTranscript plugin failed to load

## All Available Commands (first 10):
`;
        allCommands.slice(0, 10).forEach(cmd => {
            output += `- **${cmd.name}** (\`${cmd.id}\`)\n`;
        });
        
        output += `\n_... and ${allCommands.length - 10} more commands_\n`;
    }
    
    // Test if we can access the YTranscript plugin directly
    output += `\n## Plugin Detection:

`;
    
    const plugins = tp.app.plugins;
    if (plugins) {
        const ytPlugin = plugins.plugins['ytranscript'] || plugins.plugins['obsidian-ytranscript'];
        if (ytPlugin) {
            output += `✅ **YTranscript plugin found in plugins registry**
- **Plugin ID:** ${ytPlugin.manifest?.id || 'Unknown'}
- **Plugin Name:** ${ytPlugin.manifest?.name || 'Unknown'}
- **Version:** ${ytPlugin.manifest?.version || 'Unknown'}
- **Enabled:** ${plugins.enabledPlugins.has(ytPlugin.manifest?.id) ? 'Yes' : 'No'}

`;
        } else {
            output += `❌ **YTranscript plugin not found in plugins registry**

`;
        }
    }
    
    tR += output;
    
} catch (error) {
    new Notice(`Error in debug script: ${error.message}`, 5000);
    tR += `# Debug Script Error

**Error:** ${error.message}

**Stack:**
\`\`\`
${error.stack}
\`\`\`
`;
}
%>
