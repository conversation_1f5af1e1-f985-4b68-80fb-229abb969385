<%*
// Test the regex fix for YTranscript parsing
try {
    new Notice("Testing regex parsing...", 2000);
    
    // Sample YTranscript content for testing
    const sampleContent = `[0:00](https://www.youtube.com/watch?v=Xu1X-J-r5Xk) Welcome to this video about testing.
[0:15](https://www.youtube.com/watch?v=Xu1X-J-r5Xk) This is the second segment with some text.
[0:30](https://www.youtube.com/watch?v=Xu1X-J-r5Xk) Here's another segment to test parsing.
[1:00](https://www.youtube.com/watch?v=Xu1X-J-r5Xk) Final test segment with more content.`;
    
    // Test the regex parsing logic from V3 converter
    const regex = /\[(\d{1,2}:\d{2}(?::\d{2})?)\]\((https:\/\/www\.youtube\.com\/watch\?v=.*?)\) ?([\s\S]*?)(?=\[\d{1,2}:\d{2}(?::\d{2})?\]|$)/g;
    
    let output = `# Regex Parsing Test

## Sample Content:
\`\`\`
${sampleContent}
\`\`\`

## Parsing Results:

`;
    
    try {
        // Test matchAll approach
        const matches = Array.from(sampleContent.matchAll(regex));
        
        output += `### Using matchAll (${matches.length} matches found):

`;
        
        matches.forEach((match, index) => {
            const cleanText = match[3]
                .replace(/\u00a0/g, ' ')
                .replace(/\s+/g, ' ')
                .trim();
                
            output += `**Match ${index + 1}:**
- Time: ${match[1]}
- URL: ${match[2]}
- Text: "${cleanText}"

`;
        });
        
        if (matches.length === 0) {
            output += "❌ No matches found with matchAll method.\n\n";
        }
        
    } catch (matchAllError) {
        output += `❌ matchAll failed: ${matchAllError.message}\n\n`;
        
        // Test fallback line-by-line parsing
        output += `### Fallback Line-by-Line Parsing:

`;
        
        const lines = sampleContent.split('\n');
        let lineMatches = 0;
        
        for (const line of lines) {
            const match = line.match(/\[(\d{1,2}:\d{2}(?::\d{2})?)\]\((https:\/\/www\.youtube\.com\/watch\?v=.*?)\) ?(.*)/);
            if (match) {
                lineMatches++;
                const cleanText = match[3]
                    .replace(/\u00a0/g, ' ')
                    .replace(/\s+/g, ' ')
                    .trim();
                    
                output += `**Line Match ${lineMatches}:**
- Time: ${match[1]}
- Text: "${cleanText}"

`;
            }
        }
        
        if (lineMatches === 0) {
            output += "❌ No matches found with line-by-line method either.\n\n";
        }
    }
    
    // Test with actual file content if available
    const fileContent = tp.file.content || "";
    if (fileContent.includes('[') && fileContent.includes('youtube.com')) {
        output += `## Testing with Current Note Content:

`;
        
        try {
            const realMatches = Array.from(fileContent.matchAll(regex));
            output += `Found ${realMatches.length} segments in current note.

`;
            
            if (realMatches.length > 0) {
                output += `### First 3 segments:
`;
                realMatches.slice(0, 3).forEach((match, index) => {
                    const cleanText = match[3]
                        .replace(/\u00a0/g, ' ')
                        .replace(/\s+/g, ' ')
                        .trim();
                        
                    output += `**${index + 1}.** [${match[1]}] "${cleanText.substring(0, 100)}${cleanText.length > 100 ? '...' : ''}"
`;
                });
            }
        } catch (realError) {
            output += `❌ Error parsing current note: ${realError.message}
`;
        }
    } else {
        output += `## Current Note:
No YTranscript content found in current note.

`;
    }
    
    tR += output;
    
} catch (error) {
    new Notice(`Test error: ${error.message}`, 5000);
    tR += `# Test Error

**Error:** ${error.message}

**Stack:**
\`\`\`
${error.stack}
\`\`\`
`;
}
%>
