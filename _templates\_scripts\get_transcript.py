# C:\ObsidianVault\Notes-Core\_templates\_scripts\get_transcript.py
import sys
import subprocess
import os
import pathlib
import tempfile
import srt
from datetime import timedelta

def create_perfectly_synced_srt(subtitles):
    if not subtitles:
        return []

    merged_subs = []
    current_sub = subtitles[0]
    
    for i in range(1, len(subtitles)):
        next_sub = subtitles[i]
        
        time_gap = next_sub.start - current_sub.end
        ends_with_punctuation = current_sub.content.strip().endswith(('.', '?', '!'))
        is_too_long = len(current_sub.content) > 220 # Adjusted character limit

        if time_gap < timedelta(seconds=1.5) and not ends_with_punctuation and not is_too_long:
            # Merge text, keeping the original start time and updating the end time.
            current_sub.content += " " + next_sub.content.strip().replace('\n', ' ')
            current_sub.end = next_sub.end
        else:
            # --- THIS IS THE KEY FIX ---
            # Look ahead to the next subtitle's start time and make that our end time.
            # This creates a seamless, gapless transition.
            current_sub.end = next_sub.start
            # --- END OF FIX ---
            
            merged_subs.append(current_sub)
            current_sub = next_sub
            
    merged_subs.append(current_sub) # Add the final chunk

    # Clean up and re-index the final list
    for index, sub in enumerate(merged_subs, 1):
        sub.index = index
        sub.content = sub.content.replace('\n', ' ').strip()
        
    return merged_subs

def get_transcript_with_yt_dlp(url):
    try:
        python_executable = sys.executable
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = pathlib.Path(temp_dir)
            command = [
                python_executable, '-m', 'yt_dlp', '--write-auto-subs', '--sub-lang', 'en', 
                '--sub-format', 'srt', '--convert-subs', 'srt', '--skip-download', '--no-warnings',
                '-o', str(temp_path / '%(id)s'), url
            ]
            
            startupinfo = None
            if os.name == 'nt':
                startupinfo = subprocess.STARTUPINFO()
                startupinfo.dwFlags |= subprocess.STARTF_USESHOWWINDOW

            result = subprocess.run(command, capture_output=True, text=True, encoding='utf-8', startupinfo=startupinfo)

            if result.returncode != 0: return f"Error: yt-dlp failed. Details: {result.stderr.strip()}"
            srt_files = list(temp_path.glob('*.srt'))
            if not srt_files: return "Error: Subtitle file was not created."

            with open(srt_files[0], 'r', encoding='utf-8') as f:
                raw_srt = f.read()

            try:
                subs = list(srt.parse(raw_srt))
                processed_subs = create_perfectly_synced_srt(subs)
                return srt.compose(processed_subs)
            except Exception:
                return raw_srt
            
    except Exception as e:
        return f"An unexpected Python error occurred: {str(e)}"

if __name__ == "__main__":
    if len(sys.argv) < 2: 
        print("Error: Please provide a YouTube URL.", file=sys.stderr)
        sys.exit(1)
    
    result = get_transcript_with_yt_dlp(sys.argv[1])
    
    if result.strip().startswith("Error:"): 
        print(result, file=sys.stderr)
    else: 
        print(result)