# C:\ObsidianVault\Notes-Core\_templates\_scripts\test.py
import subprocess
import sys

print("--- STARTING DIRECT MODULE EXECUTION TEST ---")

try:
    python_executable = sys.executable
    print(f"Python Executable: {python_executable}")

    # --- THIS IS THE DIRECT EXECUTION COMMAND ---
    # We run 'python.exe -m yt_dlp' instead of 'yt-dlp.exe'
    command = [
        python_executable,
        '-m', 'yt_dlp',  # Execute the module directly
        '--ignore-config',
        '--header', 'User-Agent: Test',
        '--get-title',
        'https://www.youtube.com/watch?v=H14bB1z-V54'
    ]
    
    print(f"\n--- Running Direct Execution Test ---")
    result = subprocess.run(command, capture_output=True, text=True, encoding='utf-8', check=True)
    
    print(f"\n--- SUCCESS! DIRECT EXECUTION WORKS! ---")
    print(f"Video Title: {result.stdout.strip()}")
    print("--- DIAGNOSIS: The yt-dlp.exe launcher is faulty. Bypassing it is the correct solution. ---")

except subprocess.CalledProcessError as e:
    print("\n--- TEST FAILED ---")
    print("This indicates a catastrophic failure of the Python environment itself.")
    print(f"Stderr: {e.stderr}")
except Exception as e:
    print(f"\n--- AN UNEXPECTED ERROR OCCURRED: {e} ---")

print("\n--- TEST COMPLETE ---")