// C:\ObsidianVault\Notes-Core\_templates\_scripts\converter_v3.js

const { exec } = require('child_process');
const util = require('util');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');

const execAsync = util.promisify(exec);

// Helper function to generate unique IDs
function generateMxId(length = 26) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Helper function to clean text from YTranscript artifacts
function cleanTranscriptText(text) {
    if (!text) return '';

    return text
        // Replace non-breaking spaces (0xa0) with regular spaces
        .replace(/\u00a0/g, ' ')
        // Replace various Unicode spaces with regular spaces
        .replace(/[\u00a0\u1680\u2000-\u200a\u202f\u205f\u3000]/g, ' ')
        // Replace multiple consecutive spaces with single space
        .replace(/\s+/g, ' ')
        // Remove leading/trailing whitespace
        .trim()
        // Remove any remaining problematic characters
        .replace(/[^\x20-\x7E\u00A0-\uFFFF]/g, '');
}

// Helper function to format MM:SS or HH:MM:SS into HH:MM:SS,mmm
function formatSrtTime(timeStr) {
    const parts = timeStr.split(':').map(Number);
    let hours = 0, minutes, seconds;

    if (parts.length === 3) {
        [hours, minutes, seconds] = parts;
    } else {
        [minutes, seconds] = parts;
    }

    const hh = String(hours).padStart(2, '0');
    const mm = String(minutes).padStart(2, '0');
    const ss = String(seconds).padStart(2, '0');

    return `${hh}:${mm}:${ss},000`;
}

// Helper function to convert time string to total seconds
function timeToSeconds(timeStr) {
    const parts = timeStr.split(':').map(Number);
    if (parts.length === 3) {
        return parts[0] * 3600 + parts[1] * 60 + parts[2];
    } else {
        return parts[0] * 60 + parts[1];
    }
}

// Helper function to convert seconds back to SRT time format
function secondsToSrtTime(totalSeconds) {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = Math.floor(totalSeconds % 60);

    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')},000`;
}

// Function to extract video ID from various YouTube URL formats
function extractVideoId(url) {
    const patterns = [
        /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
        /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ];
    
    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) return match[1];
    }
    return null;
}

// Function to trigger YTranscript command programmatically
async function fetchYTranscriptViaCommand(tp, videoUrl) {
    new Notice("Triggering YTranscript command...", 3000);

    try {
        // First, set the URL in the note temporarily so YTranscript can process it
        const currentContent = tp.file.content || "";
        const tempContent = `${videoUrl}\n\n${currentContent}`;
        await tp.app.vault.modify(tp.file, tempContent);

        // Execute the YTranscript command - try the URL prompt version first
        const commands = tp.app.commands.listCommands();
        let ytranscriptCommand = commands.find(cmd =>
            cmd.id === 'ytranscript:transcript-from-prompt'
        );

        // Fallback to other YTranscript commands
        if (!ytranscriptCommand) {
            ytranscriptCommand = commands.find(cmd =>
                cmd.id === 'ytranscript:insert-youtube-transcript' ||
                cmd.id === 'ytranscript:transcript-from-text'
            );
        }

        if (!ytranscriptCommand) {
            throw new Error("YTranscript command not found. Please ensure YTranscript plugin is installed and enabled.");
        }

        new Notice(`Using command: ${ytranscriptCommand.name}`, 2000);

        // Execute the command with proper error handling
        try {
            if (ytranscriptCommand.callback) {
                await ytranscriptCommand.callback();
            } else if (ytranscriptCommand.checkCallback) {
                // For commands with checkCallback, we need to call them differently
                await tp.app.commands.executeCommandById(ytranscriptCommand.id);
            } else {
                await tp.app.commands.executeCommandById(ytranscriptCommand.id);
            }
        } catch (cmdError) {
            throw new Error(`Command execution failed: ${cmdError.message}`);
        }

        // Wait a moment for the command to complete
        await new Promise(resolve => setTimeout(resolve, 5000));

        // Get the updated content
        const updatedContent = tp.file.content;

        // Check if transcript was added
        if (updatedContent && updatedContent.includes('[') && updatedContent.includes('youtube.com')) {
            return updatedContent;
        } else {
            throw new Error("YTranscript command executed but no transcript content was found");
        }

    } catch (error) {
        new Notice(`Error executing YTranscript: ${error.message}`, 5000);
        throw error;
    }
}

// Alternative approach using app.commands.executeCommand instead
async function fetchYTranscriptAlternative(tp, videoUrl) {
    new Notice("Attempting alternative YTranscript execution...", 3000);
    
    try {
        // Set URL in note first
        const currentContent = tp.file.content || "";
        const tempContent = `${videoUrl}\n\n${currentContent}`;
        await tp.app.vault.modify(tp.file, tempContent);
        
        // Try different command execution method
        const commands = tp.app.commands.listCommands();
        const ytCommand = commands.find(cmd => 
            cmd.id.toLowerCase().includes('ytranscript')
        );
        
        if (!ytCommand) {
            throw new Error("YTranscript command not found");
        }
        
        // Use the command object directly
        if (ytCommand.callback) {
            await ytCommand.callback();
        } else {
            throw new Error("Command callback not available");
        }
        
        // Wait for completion
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const updatedContent = tp.file.content;
        if (updatedContent && updatedContent.includes('[') && updatedContent.includes('youtube.com')) {
            return updatedContent;
        } else {
            throw new Error("No transcript content found after command execution");
        }
        
    } catch (error) {
        throw error;
    }
}

// Updated main processing function
async function processYTranscriptV3(tp) {
    try {
        new Notice("Starting Automated YTranscript V3 Processor...", 3000);

        // Get YouTube URL from user input
        const videoUrl = await tp.system.prompt("Enter YouTube URL:");
        if (!videoUrl || !videoUrl.trim()) {
            new Notice("No URL provided. Cancelling.", 3000);
            return "";
        }

        // Validate YouTube URL
        const videoId = extractVideoId(videoUrl);
        if (!videoId) {
            new Notice("Invalid YouTube URL format.", 5000);
            return "";
        }

        const baseUrl = `https://www.youtube.com/watch?v=${videoId}`;

        // Try to fetch transcript automatically
        let transcriptContent = "";

        // First check if current note already has transcript content
        const fileContent = tp.file.content || "";
        if (fileContent.includes('[') && fileContent.includes('youtube.com')) {
            transcriptContent = fileContent;
            new Notice("Using existing transcript from current note...", 2000);
        } else {
            // Try to fetch transcript automatically
            try {
                // Attempt 1: Try primary method
                try {
                    transcriptContent = await fetchYTranscriptViaCommand(tp, baseUrl);
                    new Notice("Transcript fetched successfully!", 2000);
                } catch (primaryError) {
                    // Attempt 2: Try alternative method
                    try {
                        transcriptContent = await fetchYTranscriptAlternative(tp, baseUrl);
                        new Notice("Transcript fetched via alternative method!", 2000);
                    } catch (altError) {
                        throw primaryError; // Throw the original error
                    }
                }
            } catch (error) {
                new Notice("Automatic fetch failed. Please run YTranscript command manually first.", 6000);
                return `Please:
1. Run the YTranscript command to fetch transcript for: ${baseUrl}
2. Then run this template again to process it automatically.

Error: ${error.message}

Available YTranscript commands:
- YTranscript: Get YouTube transcript from selected url
- YTranscript: Get YouTube transcript from url prompt
- YTranscript: Insert YouTube transcript`;
            }
        }

        // Parse the YTranscript markdown format
        const regex = /\[(\d{1,2}:\d{2}(?::\d{2})?)\]\((https:\/\/www\.youtube\.com\/watch\?v=.*?)\) ?([\s\S]*?)(?=\[\d{1,2}:\d{2}(?::\d{2})?\]|$)/g;

        const parsedData = [];
        let match;

        // Reset regex lastIndex to avoid cache issues
        regex.lastIndex = 0;

        while ((match = regex.exec(transcriptContent)) !== null) {
            const cleanedText = cleanTranscriptText(match[3]);
            if (cleanedText.trim()) { // Only add non-empty segments
                parsedData.push({
                    startTime: match[1],
                    text: cleanedText
                });
            }
        }

        if (parsedData.length === 0) {
            new Notice("Error: Could not find valid YTranscript data.", 6000);
            return "Error: No valid transcript data found. Please ensure YTranscript was run successfully.";
        }
        
        new Notice(`Processing ${parsedData.length} transcript segments...`, 2000);

        // Generate SRT content with cleaned text and prevent overlapping timestamps
        let srtContent = '';
        const minSegmentDuration = 2; // Minimum 2 seconds per segment

        for (let i = 0; i < parsedData.length; i++) {
            const entry = parsedData[i];
            const nextEntry = parsedData[i + 1];

            const srtIndex = i + 1;
            const startSeconds = timeToSeconds(entry.startTime);
            const srtStartTime = formatSrtTime(entry.startTime);

            let endSeconds;
            let srtEndTime;

            if (nextEntry) {
                const nextStartSeconds = timeToSeconds(nextEntry.startTime);
                // Ensure minimum duration and prevent overlap
                endSeconds = Math.min(
                    nextStartSeconds - 0.1, // End 0.1 seconds before next segment
                    startSeconds + Math.max(minSegmentDuration, nextStartSeconds - startSeconds - 0.1)
                );
                srtEndTime = secondsToSrtTime(endSeconds);
            } else {
                // For the last segment, add a reasonable duration
                endSeconds = startSeconds + 10;
                srtEndTime = secondsToSrtTime(endSeconds);
            }

            srtContent += `${srtIndex}\n`;
            srtContent += `${srtStartTime} --> ${srtEndTime}\n`;
            srtContent += `${entry.text}\n\n`;
        }

        // Create necessary directories
        const vaultPath = tp.app.vault.adapter.basePath;
        const subtitlesDir = path.join(vaultPath, 'z_subtitles');
        const mediaLibDir = path.join(vaultPath, 'media-lib');

        try {
            await fs.mkdir(subtitlesDir, { recursive: true });
            await fs.mkdir(mediaLibDir, { recursive: true });
        } catch (err) {
            // Directories might already exist
        }

        // Generate file names and paths
        const mediaNoteUID = generateMxId();
        const shortId = crypto.randomBytes(3).toString('hex').substring(0, 4);
        const mediaNoteFileName = `url-autogen-${generateMxId(8)}.md`;
        const srtFileName = `${mediaNoteUID}.${shortId}.en.srt`;
        const srtFilePath = path.join(subtitlesDir, srtFileName);
        
        // Write SRT file
        await fs.writeFile(srtFilePath, srtContent, 'utf8');

        // Create media note for Media Extended plugin
        const mediaNoteContent = `---
mx-uid: ${mediaNoteUID}
media: ${baseUrl}
subtitles:
  - "[[${srtFileName}#lang=en&label=English (Punctuated)]]"
---
This is an auto-generated media note for the Media Extended plugin.

Video: ${baseUrl}
Transcript generated: ${new Date().toISOString()}
Segments processed: ${parsedData.length}`;

        const mediaNotePath = path.join(mediaLibDir, mediaNoteFileName);
        await fs.writeFile(mediaNotePath, mediaNoteContent, 'utf8');
        
        new Notice(`✅ Transcript processed successfully! ${parsedData.length} segments converted.`, 4000);

        // Return clean content for the current note
        return `---
video: ${baseUrl}
transcript: "[[${srtFileName}]]"
media-note: "[[${mediaNoteFileName}]]"
processed: ${new Date().toISOString()}
---

# Video Transcript

**Video:** [${baseUrl}](${baseUrl})
**Interactive Transcript:** [[${srtFileName}]]
**Media Note:** [[${mediaNoteFileName}]]

> Use the Media Extended plugin to play the video with synchronized subtitles.

---

## Transcript Preview
${parsedData.slice(0, 3).map(entry => `**${entry.startTime}:** ${entry.text}`).join('\n\n')}

${parsedData.length > 3 ? `\n_... and ${parsedData.length - 3} more segments_` : ''}
`;

    } catch (error) {
        new Notice(`Error in V3 processor: ${error.message}`, 6000);
        console.error('YTranscript V3 Error:', error);
        return `Error processing transcript: ${error.message}`;
    }
}

module.exports = processYTranscriptV3;
