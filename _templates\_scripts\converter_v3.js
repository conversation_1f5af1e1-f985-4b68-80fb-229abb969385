// C:\ObsidianVault\Notes-Core\_templates\_scripts\converter_v3.js

const { exec } = require('child_process');
const util = require('util');
const path = require('path');
const fs = require('fs').promises;
const crypto = require('crypto');

const execAsync = util.promisify(exec);

// Helper function to generate unique IDs
function generateMxId(length = 26) {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Helper function to clean text from YTranscript artifacts
function cleanTranscriptText(text) {
    return text
        // Replace non-breaking spaces (0xa0) with regular spaces
        .replace(/\u00a0/g, ' ')
        // Replace multiple consecutive spaces with single space
        .replace(/\s+/g, ' ')
        // Remove leading/trailing whitespace
        .trim();
}

// Helper function to format MM:SS or HH:MM:SS into HH:MM:SS,mmm
function formatSrtTime(timeStr) {
    const parts = timeStr.split(':').map(Number);
    let hours = 0, minutes, seconds;
    
    if (parts.length === 3) {
        [hours, minutes, seconds] = parts;
    } else {
        [minutes, seconds] = parts;
    }
    
    const hh = String(hours).padStart(2, '0');
    const mm = String(minutes).padStart(2, '0');
    const ss = String(seconds).padStart(2, '0');
    
    return `${hh}:${mm}:${ss},000`;
}

// Function to extract video ID from various YouTube URL formats
function extractVideoId(url) {
    const patterns = [
        /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
        /youtube\.com\/watch\?.*v=([^&\n?#]+)/
    ];
    
    for (const pattern of patterns) {
        const match = url.match(pattern);
        if (match) return match[1];
    }
    return null;
}

// Function to trigger YTranscript command programmatically
async function fetchYTranscriptViaCommand(tp, videoUrl) {
    new Notice("Triggering YTranscript command...", 3000);
    
    try {
        // First, set the URL in the note temporarily so YTranscript can process it
        const currentContent = tp.file.content || "";
        const tempContent = `${videoUrl}\n\n${currentContent}`;
        await tp.app.vault.modify(tp.file, tempContent);
        
        // Execute the YTranscript command
        const commands = tp.app.commands.listCommands();
        const ytranscriptCommand = commands.find(cmd => 
            cmd.id.includes('ytranscript') || 
            cmd.name.toLowerCase().includes('transcript')
        );
        
        if (!ytranscriptCommand) {
            throw new Error("YTranscript command not found. Please ensure YTranscript plugin is installed and enabled.");
        }
        
        // Execute the command with proper error handling
        try {
            await tp.app.commands.executeCommandById(ytranscriptCommand.id);
        } catch (cmdError) {
            throw new Error(`Command execution failed: ${cmdError.message}`);
        }
        
        // Wait a moment for the command to complete
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        // Get the updated content
        const updatedContent = tp.file.content;
        
        // Check if transcript was added
        if (updatedContent && updatedContent.includes('[') && updatedContent.includes('youtube.com')) {
            return updatedContent;
        } else {
            throw new Error("YTranscript command executed but no transcript content was found");
        }
        
    } catch (error) {
        new Notice(`Error executing YTranscript: ${error.message}`, 5000);
        throw error;
    }
}

// Alternative approach using app.commands.executeCommand instead
async function fetchYTranscriptAlternative(tp, videoUrl) {
    new Notice("Attempting alternative YTranscript execution...", 3000);
    
    try {
        // Set URL in note first
        const currentContent = tp.file.content || "";
        const tempContent = `${videoUrl}\n\n${currentContent}`;
        await tp.app.vault.modify(tp.file, tempContent);
        
        // Try different command execution method
        const commands = tp.app.commands.listCommands();
        const ytCommand = commands.find(cmd => 
            cmd.id.toLowerCase().includes('ytranscript')
        );
        
        if (!ytCommand) {
            throw new Error("YTranscript command not found");
        }
        
        // Use the command object directly
        if (ytCommand.callback) {
            await ytCommand.callback();
        } else {
            throw new Error("Command callback not available");
        }
        
        // Wait for completion
        await new Promise(resolve => setTimeout(resolve, 3000));
        
        const updatedContent = tp.file.content;
        if (updatedContent && updatedContent.includes('[') && updatedContent.includes('youtube.com')) {
            return updatedContent;
        } else {
            throw new Error("No transcript content found after command execution");
        }
        
    } catch (error) {
        throw error;
    }
}

// Updated main processing function
async function processYTranscriptV3(tp) {
    try {
        new Notice("Starting Automated YTranscript V3 Processor...", 3000);

        // Get YouTube URL from user input
        const videoUrl = await tp.system.prompt("Enter YouTube URL:");
        if (!videoUrl || !videoUrl.trim()) {
            new Notice("No URL provided. Cancelling.", 3000);
            return "";
        }

        // Validate YouTube URL
        const videoId = extractVideoId(videoUrl);
        if (!videoId) {
            new Notice("Invalid YouTube URL format.", 5000);
            return "";
        }

        const baseUrl = `https://www.youtube.com/watch?v=${videoId}`;

        // Try to fetch transcript automatically
        let transcriptContent = "";
        try {
            // Attempt 1: Try to fetch using YTranscript command programmatically
            transcriptContent = await fetchYTranscriptViaCommand(tp, baseUrl);
            new Notice("Transcript fetched successfully!", 2000);
        } catch (error) {
            // Attempt 2: Check if current note already has transcript content
            const fileContent = tp.file.content;
            if (fileContent && fileContent.includes('[') && fileContent.includes('youtube.com')) {
                transcriptContent = fileContent;
                new Notice("Using existing transcript from current note...", 2000);
            } else {
                new Notice("Automatic fetch failed. Please run YTranscript command manually first.", 6000);
                return `Please:
1. Run the YTranscript command to fetch transcript for: ${baseUrl}
2. Then run this template again to process it automatically.

Error: ${error.message}`;
            }
        }

        // Parse the YTranscript markdown format
        const regex = /\[(\d{1,2}:\d{2}(?::\d{2})?)\]\((https:\/\/www\.youtube\.com\/watch\?v=.*?)\) ?([\s\S]*?)(?=\[\d{1,2}:\d{2}(?::\d{2})?\]|$)/g;
        
        const parsedData = [];
        let match;

        while ((match = regex.exec(transcriptContent)) !== null) {
            parsedData.push({
                startTime: match[1],
                text: cleanTranscriptText(match[3])
            });
        }

        if (parsedData.length === 0) {
            new Notice("Error: Could not find valid YTranscript data.", 6000);
            return "Error: No valid transcript data found. Please ensure YTranscript was run successfully.";
        }
        
        new Notice(`Processing ${parsedData.length} transcript segments...`, 2000);

        // Generate SRT content with cleaned text
        let srtContent = '';
        for (let i = 0; i < parsedData.length; i++) {
            const entry = parsedData[i];
            const nextEntry = parsedData[i + 1];

            const srtIndex = i + 1;
            const srtStartTime = formatSrtTime(entry.startTime);
            
            let srtEndTime;
            if (nextEntry) {
                srtEndTime = formatSrtTime(nextEntry.startTime);
            } else {
                // Calculate end time for the last segment
                const timeParts = entry.startTime.split(':').map(Number);
                let totalSeconds = 0;
                if (timeParts.length === 3) {
                    totalSeconds = timeParts[0] * 3600 + timeParts[1] * 60 + timeParts[2];
                } else {
                    totalSeconds = timeParts[0] * 60 + timeParts[1];
                }
                totalSeconds += 10; // Add 10 seconds duration
                
                const endHours = Math.floor(totalSeconds / 3600);
                const endMinutes = Math.floor((totalSeconds % 3600) / 60);
                const endSecondsRemainder = totalSeconds % 60;
                
                srtEndTime = `${String(endHours).padStart(2, '0')}:${String(endMinutes).padStart(2, '0')}:${String(endSecondsRemainder).padStart(2, '0')},000`;
            }

            srtContent += `${srtIndex}\n`;
            srtContent += `${srtStartTime} --> ${srtEndTime}\n`;
            srtContent += `${entry.text}\n\n`;
        }

        // Create necessary directories
        const vaultPath = tp.app.vault.adapter.basePath;
        const subtitlesDir = path.join(vaultPath, 'z_subtitles');
        const mediaLibDir = path.join(vaultPath, 'media-lib');

        try {
            await fs.mkdir(subtitlesDir, { recursive: true });
            await fs.mkdir(mediaLibDir, { recursive: true });
        } catch (err) {
            // Directories might already exist
        }

        // Generate file names and paths
        const mediaNoteUID = generateMxId();
        const shortId = crypto.randomBytes(3).toString('hex').substring(0, 4);
        const mediaNoteFileName = `url-autogen-${generateMxId(8)}.md`;
        const srtFileName = `${mediaNoteUID}.${shortId}.en.srt`;
        const srtFilePath = path.join(subtitlesDir, srtFileName);
        
        // Write SRT file
        await fs.writeFile(srtFilePath, srtContent, 'utf8');

        // Create media note for Media Extended plugin
        const mediaNoteContent = `---
mx-uid: ${mediaNoteUID}
media: ${baseUrl}
subtitles:
  - "[[${srtFileName}#lang=en&label=English (Punctuated)]]"
---
This is an auto-generated media note for the Media Extended plugin.

Video: ${baseUrl}
Transcript generated: ${new Date().toISOString()}
Segments processed: ${parsedData.length}`;

        const mediaNotePath = path.join(mediaLibDir, mediaNoteFileName);
        await fs.writeFile(mediaNotePath, mediaNoteContent, 'utf8');
        
        new Notice(`✅ Transcript processed successfully! ${parsedData.length} segments converted.`, 4000);

        // Return clean content for the current note
        return `---
video: ${baseUrl}
transcript: "[[${srtFileName}]]"
media-note: "[[${mediaNoteFileName}]]"
processed: ${new Date().toISOString()}
---

# Video Transcript

**Video:** [${baseUrl}](${baseUrl})
**Interactive Transcript:** [[${srtFileName}]]
**Media Note:** [[${mediaNoteFileName}]]

> Use the Media Extended plugin to play the video with synchronized subtitles.

---

## Transcript Preview
${parsedData.slice(0, 3).map(entry => `**${entry.startTime}:** ${entry.text}`).join('\n\n')}

${parsedData.length > 3 ? `\n_... and ${parsedData.length - 3} more segments_` : ''}
`;

    } catch (error) {
        new Notice(`Error in V3 processor: ${error.message}`, 6000);
        console.error('YTranscript V3 Error:', error);
        return `Error processing transcript: ${error.message}`;
    }
}

module.exports = processYTranscriptV3;
